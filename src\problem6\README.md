# Live Scoreboard API Service Module

## Overview

This module provides a real-time scoreboard system that displays the top 10 user scores with live updates. The system ensures secure score updates through proper authentication and authorization mechanisms while providing real-time data synchronization across all connected clients.

## Architecture Components

### Core Components

1. **Score Management Service** - Handles score updates and validation
2. **Real-time Communication Layer** - WebSocket-based live updates
3. **Authentication & Authorization** - Secure user verification
4. **Leaderboard Cache** - High-performance score ranking
5. **Database Layer** - Persistent score storage
6. **Rate Limiting** - Anti-abuse protection

## API Endpoints

### 1. Score Update Endpoint
```
POST /api/scores/update
```

**Purpose**: Update user score after completing an action

**Headers**:
- `Authorization: Bearer <jwt_token>`
- `Content-Type: application/json`

**Request Body**:
```json
{
  "actionId": "string",
  "scoreIncrement": "number",
  "timestamp": "ISO8601",
  "actionSignature": "string"
}
```

**Response**:
```json
{
  "success": true,
  "newScore": 1250,
  "rank": 5,
  "message": "Score updated successfully"
}
```

### 2. Leaderboard Retrieval
```
GET /api/leaderboard/top10
```

**Purpose**: Get current top 10 scores

**Response**:
```json
{
  "leaderboard": [
    {
      "rank": 1,
      "userId": "user123",
      "username": "player1",
      "score": 2500,
      "lastUpdated": "2024-01-15T10:30:00Z"
    }
  ],
  "lastRefresh": "2024-01-15T10:30:00Z"
}
```

### 3. WebSocket Connection
```
WS /ws/leaderboard
```

**Purpose**: Real-time leaderboard updates

**Authentication**: JWT token via query parameter or header

**Events**:
- `leaderboard_update`: Broadcast to all clients when top 10 changes
- `user_score_update`: Sent to specific user when their score changes (regardless of ranking)
- `rank_change`: Sent to specific user when their position changes in overall rankings

## Security Measures

### 1. Authentication
- JWT-based user authentication
- Token expiration and refresh mechanism
- Secure token storage recommendations

### 2. Authorization
- Action-based permissions
- User role verification
- Score increment validation

### 3. Anti-Fraud Protection
- **Action Signature Verification**: Each score update must include a cryptographic signature
- **Rate Limiting**: Maximum score updates per user per time window
- **Anomaly Detection**: Unusual scoring patterns flagged for review
- **Action Validation**: Server-side verification of completed actions

### 4. Input Validation
- Score increment bounds checking
- Timestamp validation (prevent replay attacks)
- Action ID verification against valid actions

## Data Models

### User Score Model
```javascript
{
  userId: String,
  username: String,
  totalScore: Number,
  lastActionTimestamp: Date,
  actionHistory: [{
    actionId: String,
    scoreGained: Number,
    timestamp: Date,
    verified: Boolean
  }]
}
```

### Leaderboard Cache Model
```javascript
{
  rank: Number,
  userId: String,
  username: String,
  score: Number,
  lastUpdated: Date
}
```

## Real-time Updates Flow

1. User completes action on frontend
2. Frontend generates action signature
3. API call sent to `/api/scores/update`
4. Server validates authentication and action
5. Score updated in database
6. **Always send `user_score_update` to the specific user** (regardless of ranking)
7. Check if top 10 leaderboard affected
8. If top 10 changed: broadcast `leaderboard_update` to all connected clients
9. Frontend updates user's personal score and leaderboard display

## Performance Considerations

### Caching Strategy
- **Redis Cache**: Top 10 leaderboard cached for fast retrieval
- **Cache TTL**: 30 seconds for leaderboard data
- **Cache Invalidation**: Triggered on score updates affecting top 10

### Database Optimization
- **Indexed Queries**: Optimized for score-based sorting
- **Read Replicas**: Separate read/write operations
- **Batch Updates**: Group multiple score updates when possible

### WebSocket Management
- **Connection Pooling**: Efficient connection management
- **Message Queuing**: Handle high-frequency updates
- **Graceful Degradation**: Fallback to polling if WebSocket fails

### Broadcasting Strategy

#### Individual User Updates (Always Sent)
- **Target**: Specific user who performed the action
- **Trigger**: Every score update, regardless of ranking
- **Purpose**: Immediate feedback and score confirmation
- **Event Type**: `user_score_update`

#### Leaderboard Updates (Conditional Broadcast)
- **Target**: All connected clients
- **Trigger**: Only when top 10 rankings change
- **Purpose**: Keep leaderboard display current for all users
- **Event Type**: `leaderboard_update`

#### Implementation Logic
```javascript
// After score update
async function handleScoreUpdate(userId, newScore, scoreIncrement) {
    // 1. Always notify the user of their score change
    await sendToUser(userId, {
        type: 'user_score_update',
        data: { newScore, scoreIncrement, timestamp: new Date() }
    });

    // 2. Check if top 10 affected
    const top10Changed = await updateLeaderboardCache(userId, newScore);

    // 3. Broadcast leaderboard update only if top 10 changed
    if (top10Changed) {
        const updatedLeaderboard = await getTop10Leaderboard();
        await broadcastToAll({
            type: 'leaderboard_update',
            data: { leaderboard: updatedLeaderboard, timestamp: new Date() }
        });
    }
}
```

## Error Handling

### Common Error Responses
```json
{
  "error": {
    "code": "INVALID_ACTION",
    "message": "Action signature verification failed",
    "details": "The provided action signature is invalid or expired"
  }
}
```

### Error Codes
- `UNAUTHORIZED`: Invalid or expired authentication
- `INVALID_ACTION`: Action verification failed
- `RATE_LIMITED`: Too many requests
- `INVALID_SCORE`: Score increment out of bounds
- `SERVER_ERROR`: Internal server error

## Monitoring & Logging

### Key Metrics
- Score update frequency per user
- WebSocket connection count
- API response times
- Cache hit/miss ratios
- Failed authentication attempts

### Logging Requirements
- All score updates with user ID and timestamp
- Failed authentication attempts
- Suspicious activity patterns
- System performance metrics

## Deployment Considerations

### Environment Variables
```
JWT_SECRET=<secret_key>
REDIS_URL=<redis_connection_string>
DATABASE_URL=<database_connection_string>
RATE_LIMIT_WINDOW=3600
RATE_LIMIT_MAX_REQUESTS=100
WEBSOCKET_PORT=8080
```

### Scaling Recommendations
- **Horizontal Scaling**: Multiple API server instances
- **Load Balancing**: Distribute WebSocket connections
- **Database Sharding**: Partition user data by user ID ranges
- **CDN Integration**: Cache static leaderboard data

## Testing Strategy

### Unit Tests
- Score calculation logic
- Authentication middleware
- Input validation functions
- Cache operations

### Integration Tests
- End-to-end score update flow
- WebSocket connection handling
- Database operations
- Redis cache integration

### Load Testing
- Concurrent score updates
- WebSocket connection limits
- Database performance under load
- Cache performance testing

## Future Improvements

### Enhanced Security
1. **Blockchain Integration**: Immutable score history
2. **Machine Learning**: Advanced fraud detection
3. **Multi-factor Authentication**: Additional security layer
4. **Audit Trails**: Comprehensive action logging

### Performance Enhancements
1. **GraphQL Integration**: Flexible data querying
2. **Event Sourcing**: Complete action history
3. **Microservices Architecture**: Service separation
4. **Edge Computing**: Distributed score processing

### User Experience
1. **Real-time Notifications**: Score milestone alerts
2. **Historical Analytics**: Score progression tracking
3. **Social Features**: Friend leaderboards
4. **Achievement System**: Gamification elements

### Monitoring & Analytics
1. **Advanced Metrics**: User engagement analytics
2. **Predictive Analytics**: Score trend analysis
3. **A/B Testing**: Feature experimentation
4. **Real-time Dashboards**: System health monitoring

## Implementation Timeline

### Phase 1 (Weeks 1-2): Core Infrastructure
- Basic API endpoints
- Database schema
- Authentication system
- Basic WebSocket implementation

### Phase 2 (Weeks 3-4): Security & Validation
- Action signature verification
- Rate limiting implementation
- Input validation
- Anti-fraud measures

### Phase 3 (Weeks 5-6): Performance & Caching
- Redis integration
- Database optimization
- WebSocket scaling
- Load testing

### Phase 4 (Weeks 7-8): Monitoring & Deployment
- Logging implementation
- Monitoring setup
- Production deployment
- Documentation finalization

## Technical Implementation Details

### Database Schema

#### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    total_score INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

CREATE INDEX idx_users_total_score ON users(total_score DESC);
CREATE INDEX idx_users_username ON users(username);
```

#### User Scores Table
```sql
CREATE TABLE user_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    action_id VARCHAR(100) NOT NULL,
    score_increment INTEGER NOT NULL,
    total_score_after INTEGER NOT NULL,
    action_signature VARCHAR(500) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_user_scores_user_id ON user_scores(user_id);
CREATE INDEX idx_user_scores_timestamp ON user_scores(timestamp DESC);
CREATE INDEX idx_user_scores_verified ON user_scores(verified);
```

#### Action Types Table
```sql
CREATE TABLE action_types (
    id VARCHAR(100) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    max_score_increment INTEGER NOT NULL,
    min_score_increment INTEGER DEFAULT 1,
    rate_limit_per_hour INTEGER DEFAULT 10,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### WebSocket Event Specifications

#### Leaderboard Update Event
```json
{
  "type": "leaderboard_update",
  "data": {
    "leaderboard": [
      {
        "rank": 1,
        "userId": "user123",
        "username": "player1",
        "score": 2500,
        "scoreChange": "+50",
        "lastUpdated": "2024-01-15T10:30:00Z"
      }
    ],
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

#### User Score Update Event
```json
{
  "type": "user_score_update",
  "data": {
    "userId": "user123",
    "newScore": 1250,
    "scoreIncrement": 25,
    "newRank": 5,
    "previousRank": 7,
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### Action Signature Algorithm

The action signature prevents unauthorized score manipulation by requiring cryptographic proof of action completion.

#### Signature Generation (Frontend)
```javascript
// Frontend signature generation
function generateActionSignature(actionId, userId, timestamp, secretKey) {
    const payload = `${actionId}:${userId}:${timestamp}`;
    const signature = CryptoJS.HmacSHA256(payload, secretKey).toString();
    return signature;
}
```

#### Signature Verification (Backend)
```javascript
// Backend signature verification
function verifyActionSignature(actionId, userId, timestamp, signature, secretKey) {
    const expectedPayload = `${actionId}:${userId}:${timestamp}`;
    const expectedSignature = crypto
        .createHmac('sha256', secretKey)
        .update(expectedPayload)
        .digest('hex');

    // Prevent timing attacks
    return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
    );
}
```

### Rate Limiting Implementation

#### Redis-based Rate Limiting
```javascript
// Rate limiting using Redis sliding window
async function checkRateLimit(userId, actionId, windowSize = 3600, maxRequests = 10) {
    const key = `rate_limit:${userId}:${actionId}`;
    const now = Date.now();
    const windowStart = now - (windowSize * 1000);

    // Remove old entries
    await redis.zremrangebyscore(key, 0, windowStart);

    // Count current requests
    const currentCount = await redis.zcard(key);

    if (currentCount >= maxRequests) {
        return { allowed: false, remaining: 0, resetTime: windowStart + (windowSize * 1000) };
    }

    // Add current request
    await redis.zadd(key, now, `${now}-${Math.random()}`);
    await redis.expire(key, windowSize);

    return { allowed: true, remaining: maxRequests - currentCount - 1 };
}
```

### Caching Strategy Details

#### Leaderboard Cache Management
```javascript
// Cache key structure
const CACHE_KEYS = {
    TOP_10: 'leaderboard:top10',
    USER_RANK: (userId) => `user:${userId}:rank`,
    USER_SCORE: (userId) => `user:${userId}:score`
};

// Cache update on score change
async function updateLeaderboardCache(userId, newScore) {
    // Update user's cached score
    await redis.set(CACHE_KEYS.USER_SCORE(userId), newScore, 'EX', 300);

    // Check if user is in top 10
    const currentTop10 = await redis.get(CACHE_KEYS.TOP_10);
    const leaderboard = JSON.parse(currentTop10 || '[]');

    const userInTop10 = leaderboard.find(entry => entry.userId === userId);
    const lowestInTop10 = leaderboard[9]?.score || 0;

    if (userInTop10 || newScore > lowestInTop10) {
        // Refresh top 10 from database
        const freshTop10 = await getTop10FromDatabase();
        await redis.set(CACHE_KEYS.TOP_10, JSON.stringify(freshTop10), 'EX', 30);
        return true; // Indicates top 10 changed
    }

    return false; // Top 10 unchanged
}
```

## Additional Security Considerations

### 1. Advanced Fraud Detection

#### Behavioral Analysis
- **Scoring Velocity**: Flag users with unusually high scoring rates
- **Pattern Recognition**: Detect repetitive action patterns
- **Time-based Analysis**: Identify suspicious timing patterns
- **Cross-user Correlation**: Detect coordinated attacks

#### Implementation Example
```javascript
async function detectAnomalousActivity(userId, actionId, scoreIncrement) {
    const recentActions = await getUserRecentActions(userId, 3600); // Last hour

    // Check scoring velocity
    const totalScoreInHour = recentActions.reduce((sum, action) => sum + action.scoreIncrement, 0);
    const averageUserScore = await getAverageHourlyScore();

    if (totalScoreInHour > averageUserScore * 5) {
        await flagSuspiciousActivity(userId, 'HIGH_SCORING_VELOCITY', {
            hourlyScore: totalScoreInHour,
            average: averageUserScore
        });
    }

    // Check action pattern repetition
    const actionPattern = recentActions.map(a => a.actionId).join(',');
    const patternFrequency = await getPatternFrequency(actionPattern);

    if (patternFrequency > 0.8) { // 80% similarity threshold
        await flagSuspiciousActivity(userId, 'REPETITIVE_PATTERN', {
            pattern: actionPattern,
            frequency: patternFrequency
        });
    }
}
```

### 2. Data Integrity Measures

#### Score Audit Trail
```javascript
// Comprehensive audit logging
async function auditScoreUpdate(userId, actionId, scoreIncrement, metadata) {
    const auditEntry = {
        userId,
        actionId,
        scoreIncrement,
        timestamp: new Date().toISOString(),
        ipAddress: metadata.ipAddress,
        userAgent: metadata.userAgent,
        sessionId: metadata.sessionId,
        previousScore: metadata.previousScore,
        newScore: metadata.newScore,
        verificationStatus: 'PENDING'
    };

    await auditLogger.log('SCORE_UPDATE', auditEntry);
}
```

#### Periodic Score Reconciliation
```javascript
// Daily score verification job
async function reconcileUserScores() {
    const users = await getAllActiveUsers();

    for (const user of users) {
        const calculatedScore = await calculateUserScoreFromHistory(user.id);
        const storedScore = user.total_score;

        if (calculatedScore !== storedScore) {
            await flagScoreDiscrepancy(user.id, {
                calculated: calculatedScore,
                stored: storedScore,
                difference: Math.abs(calculatedScore - storedScore)
            });
        }
    }
}
```

## Conclusion

This specification provides a comprehensive foundation for implementing a secure, scalable, and real-time scoreboard system. The modular architecture ensures maintainability while the security measures protect against common attack vectors. The performance optimizations enable the system to handle high-frequency updates while maintaining real-time responsiveness.

Key strengths of this design:
- **Security-first approach** with multiple layers of protection
- **Scalable architecture** supporting horizontal growth
- **Real-time capabilities** with WebSocket integration
- **Comprehensive monitoring** for operational excellence
- **Fraud prevention** through behavioral analysis and audit trails

The implementation timeline provides a structured approach to development, ensuring core functionality is delivered first while advanced features are added incrementally.

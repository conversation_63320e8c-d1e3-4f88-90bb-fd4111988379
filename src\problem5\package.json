{"name": "simple-crud-api", "version": "1.0.0", "description": "A super simple CRUD API with organized structure", "main": "server.js", "scripts": {"start": "node server.js", "test": "mocha test/api.test.js --timeout 5000"}, "keywords": ["express", "crud", "api", "simple"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "sqlite3": "^5.1.6"}, "devDependencies": {"mocha": "^11.7.1", "chai": "^4.3.10", "chai-http": "^4.4.0"}}
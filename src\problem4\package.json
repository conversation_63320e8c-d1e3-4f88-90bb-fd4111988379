{"name": "problem4-sum-to-n", "version": "1.0.0", "description": "Three ways to calculate sum from 1 to n", "main": "sum_to_n.ts", "scripts": {"start": "npx ts-node interactive_sum.ts", "run": "npx ts-node run.ts", "test": "npx ts-node test_sum_to_n.ts", "interactive": "npx ts-node interactive_sum.ts"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "ts-node": "^10.0.0"}, "keywords": ["sum", "algorithms", "typescript", "math"], "author": "", "license": "MIT"}
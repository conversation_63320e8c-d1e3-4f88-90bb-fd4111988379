# Simple CRUD API with SQLite Database

A clean, organized ExpressJS CRUD API with separate folders for configs, services, controllers, and routes. Features automatic SQLite database initialization.

## 📁 Project Structure

```
src/problem5/
├── server.js              # Main server file
├── configs/               # Configuration files
│   └── database.js        # SQLite database config & initialization
├── services/              # Business logic
│   └── resourceService.js
├── controllers/           # HTTP request handlers  
│   └── resourceController.js
├── routes/               # API route definitions
│   └── resourceRoutes.js
├── test/                 # Chai HTTP tests
│   └── api.test.js
├── data/                 # Database files (auto-created)
│   └── resources.db      # SQLite database
├── package.json
└── README.md
```

## 🚀 Getting Started

### Install Dependencies
```bash
npm install
```

### Start the Server
```bash
npm start
```
Server runs on `http://localhost:3000`

**Database Auto-Initialization:**
- SQLite database file is automatically created in `data/resources.db`
- Tables are created automatically on first run
- No manual database setup required!

### Run Tests
```bash
npm test
```

## 📋 API Endpoints

### Health Check
- **GET** `/health` - Check if server is running

### Resources
- **GET** `/api/resources` - Get all resources
- **GET** `/api/resources/:id` - Get resource by ID
- **POST** `/api/resources` - Create new resource
- **PUT** `/api/resources/:id` - Update resource
- **DELETE** `/api/resources/:id` - Delete resource

### Query Parameters (GET /api/resources)
- `category` - Filter by category
- `status` - Filter by status (active/inactive)
- `search` - Search in name and description

## 📝 Example Usage

### Create a Resource
```bash
curl -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Resource",
    "description": "A sample resource",
    "category": "Example"
  }'
```

### Get All Resources
```bash
curl http://localhost:3000/api/resources
```

### Filter Resources
```bash
curl "http://localhost:3000/api/resources?category=Example&status=active"
```

## 🗄️ Database Features

### **Automatic Initialization**
- Database file created automatically if not present
- Tables created on startup
- No manual setup required

### **SQLite Configuration** (`configs/database.js`)
- Singleton database connection
- Promise-based query methods
- Automatic table creation
- Data clearing for tests

### **Database Schema**
```sql
CREATE TABLE resources (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL DEFAULT 'General',
    status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'inactive')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 🧪 Testing

The project includes comprehensive Chai HTTP tests covering:
- ✅ Health check
- ✅ CRUD operations (Create, Read, Update, Delete)
- ✅ Input validation
- ✅ Error handling (404, 400)
- ✅ Resource filtering
- ✅ Database operations

Run tests with: `npm test`

## 🏗️ Architecture

### **Configs Layer** (`configs/`)
- Database configuration and initialization
- Auto-creates database file and tables
- Provides database connection singleton

### **Services Layer** (`services/`)
- Contains business logic
- Handles database operations
- Validates input
- Returns structured responses

### **Controllers Layer** (`controllers/`)
- Handles HTTP requests/responses
- Calls service methods
- Manages HTTP status codes
- Handles errors

### **Routes Layer** (`routes/`)
- Defines API endpoints
- Maps URLs to controller methods
- Clean separation of concerns

## 🎯 Features

- **Simple & Clean**: Easy to understand and modify
- **Organized Structure**: Separate concerns with clear folders
- **SQLite Database**: Persistent storage with auto-initialization
- **Comprehensive Tests**: Full test coverage with Chai
- **Error Handling**: Proper HTTP status codes and error messages
- **Filtering**: Search and filter resources easily
- **Auto-Setup**: No manual database configuration needed

## 🔧 Development

The codebase is designed to be:
- **Human-readable**: Clear, simple code
- **Easy to extend**: Add new features easily
- **Well-tested**: Reliable with good test coverage
- **Organized**: Clean separation of concerns
- **Production-ready**: Proper database with persistence

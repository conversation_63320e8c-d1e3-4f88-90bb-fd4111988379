
## 📁 Project Structure

```
src/problem5/
├── server.js              # Main server file
├── configs/               # Configuration files
│   └── database.js        # SQLite database config & initialization
├── services/              # Business logic
│   └── resourceService.js
├── controllers/           # HTTP request handlers  
│   └── resourceController.js
├── routes/               # API route definitions
│   └── resourceRoutes.js
├── test/                 # Chai HTTP tests
│   └── api.test.js
├── data/                 # Database files (auto-created)
│   └── resources.db      # SQLite database
├── package.json
└── README.md
```

## 🚀 Getting Started

### Install Dependencies
```bash
npm install
```

### Start the Server
```bash
npm start
```
Server runs on `http://localhost:3000`

**Database Auto-Initialization:**
- SQLite database file is automatically created in `data/resources.db`
- Tables are created automatically on first run
- No manual database setup required!

### Run Tests
```bash
npm test
```

## 📋 API Endpoints

### Health Check
- **GET** `/health` - Check if server is running

### Resources
- **GET** `/api/resources` - Get all resources
- **GET** `/api/resources/:id` - Get resource by ID
- **POST** `/api/resources` - Create new resource
- **PUT** `/api/resources/:id` - Update resource
- **DELETE** `/api/resources/:id` - Delete resource

### Query Parameters (GET /api/resources)
- `category` - Filter by category
- `status` - Filter by status (active/inactive)
- `search` - Search in name and description

## 📝 Example Usage

### Create a Resource
```bash
curl -X POST http://localhost:3000/api/resources \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Resource",
    "description": "A sample resource",
    "category": "Example"
  }'
```

### Get All Resources
```bash
curl http://localhost:3000/api/resources
```

### Filter Resources
```bash
curl "http://localhost:3000/api/resources?category=Example&status=active"
```


### **Database Schema**
```sql
CREATE TABLE resources (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL DEFAULT 'General',
    status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'inactive')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 🧪 Testing

The project includes comprehensive Chai HTTP tests covering:
- ✅ Health check
- ✅ CRUD operations (Create, Read, Update, Delete)
- ✅ Input validation
- ✅ Error handling (404, 400)
- ✅ Resource filtering
- ✅ Database operations

Run tests with: `npm test`


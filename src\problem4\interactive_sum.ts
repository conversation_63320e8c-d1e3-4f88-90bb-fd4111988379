import * as readline from 'readline';
import { sum_to_n_a, sum_to_n_b, sum_to_n_c } from './sum_to_n';

// Create readline interface for user input
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Function to display menu
function displayMenu(): void {
    console.log('\n=== Sum to N Calculator ===');
    console.log('Calculate the sum of integers from 1 to n');
    console.log('Available methods:');
    console.log('  A - Math Formula (Fastest - O(1))');
    console.log('  B - Loop (Medium - O(n))');
    console.log('  C - Recursion (Slowest - O(n) time, O(n) space)');
    console.log('  ALL - Compare all three methods');
    console.log('  Q - Quit');
    console.log('=====================================\n');
}

// Function to get performance timing
function measurePerformance(fn: () => number): { result: number; time: number } {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    return { result, time: end - start };
}

// Function to run a specific method
function runMethod(method: string, n: number): void {
    console.log(`\nCalculating sum from 1 to ${n}:\n`);
    
    switch (method.toUpperCase()) {
        case 'A':
            const resultA = measurePerformance(() => sum_to_n_a(n));
            console.log(`Method A (Formula): ${resultA.result}`);
            console.log(`Time: ${resultA.time.toFixed(4)} ms`);
            console.log(`Formula: n(n+1)/2 = ${n}(${n}+1)/2 = ${resultA.result}`);
            break;
            
        case 'B':
            const resultB = measurePerformance(() => sum_to_n_b(n));
            console.log(`Method B (Loop): ${resultB.result}`);
            console.log(`Time: ${resultB.time.toFixed(4)} ms`);
            console.log(`Process: 1 + 2 + 3 + ... + ${n} = ${resultB.result}`);
            break;
            
        case 'C':
            const resultC = measurePerformance(() => sum_to_n_c(n));
            console.log(`Method C (Recursion): ${resultC.result}`);
            console.log(`Time: ${resultC.time.toFixed(4)} ms`);
            console.log(`Process: ${n} + sum_to_n(${n-1}) = ${resultC.result}`);
            break;
            
        case 'ALL':
            const perfA = measurePerformance(() => sum_to_n_a(n));
            const perfB = measurePerformance(() => sum_to_n_b(n));
            const perfC = measurePerformance(() => sum_to_n_c(n));
            
            console.log('=== COMPARISON ===');
            console.log(`Method A (Formula):   ${perfA.result.toString().padStart(10)} | ${perfA.time.toFixed(4).padStart(8)} ms`);
            console.log(`Method B (Loop):      ${perfB.result.toString().padStart(10)} | ${perfB.time.toFixed(4).padStart(8)} ms`);
            console.log(`Method C (Recursion): ${perfC.result.toString().padStart(10)} | ${perfC.time.toFixed(4).padStart(8)} ms`);
            console.log('==================');
            
            // Find fastest method
            const fastest = Math.min(perfA.time, perfB.time, perfC.time);
            if (perfA.time === fastest) console.log('🏆 Method A is fastest!');
            else if (perfB.time === fastest) console.log('🏆 Method B is fastest!');
            else console.log('🏆 Method C is fastest!');
            break;
            
        default:
            console.log('Invalid method. Please choose A, B, C, or ALL.');
    }
}

// Main interactive loop
function startInteractive(): void {
    displayMenu();
    
    rl.question('Choose a method (A/B/C/ALL) or Q to quit: ', (method) => {
        if (method.toUpperCase() === 'Q') {
            console.log('Goodbye! 👋');
            rl.close();
            return;
        }
        
        if (!['A', 'B', 'C', 'ALL'].includes(method.toUpperCase())) {
            console.log('Invalid choice. Please try again.');
            startInteractive();
            return;
        }
        
        rl.question('Enter a positive integer n: ', (input) => {
            const n = parseInt(input);
            
            if (isNaN(n)) {
                console.log('Please enter a valid number.');
                startInteractive();
                return;
            }
            
            if (n < 0) {
                console.log('Note: For negative numbers, the result will be 0.');
            }
            
            try {
                runMethod(method, n);
            } catch (error) {
                console.log(`Error: ${error}`);
                if (n > 10000 && method.toUpperCase() === 'C') {
                    console.log('Note: Large numbers with recursion may cause stack overflow.');
                }
            }
            
            console.log('\n' + '='.repeat(40));
            startInteractive();
        });
    });
}

// Start the program
console.log('Welcome to the Sum to N Calculator! 🧮');
startInteractive();

# Problem 4: Three Ways to Sum to n

Calculate the sum of integers from 1 to n: `sum_to_n(5) = 1 + 2 + 3 + 4 + 5 = 15`

## Three Methods

**Method A - Math Formula (FASTEST)**
```typescript
function sum_to_n_a(n: number): number {
    if (n <= 0) return 0;
    return (n * (n + 1)) / 2;  // Uses formula: n(n+1)/2
}
```
- Time: O(1) - instant
- Space: O(1) - no extra memory

**Method B - Loop (MEDIUM)**
```typescript
function sum_to_n_b(n: number): number {
    if (n <= 0) return 0;
    let sum = 0;
    for (let i = 1; i <= n; i++) {
        sum += i;
    }
    return sum;
}
```
- Time: O(n) - depends on n
- Space: O(1) - no extra memory

**Method C - Recursion (SLOWEST)**
```typescript
function sum_to_n_c(n: number): number {
    if (n <= 0) return 0;
    return n + sum_to_n_c(n - 1);
}
```
- Time: O(n) - depends on n
- Space: O(n) - uses call stack

## Quick Test

```bash
npx ts-node test_sum_to_n.ts
```

## Which to Use?

- **Method A**: Best for real code (fastest)
- **Method B**: Good for learning (easy to understand)
- **Method C**: Academic/educational (shows recursion)

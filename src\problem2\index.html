<html>

<head>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Fancy Form</title>

  <!-- You may add more stuff here -->
  <link href="style.css" rel="stylesheet" />
</head>

<body>

  <!-- You may reorganise the whole HTML, as long as your form achieves the same effect. -->
  <form onsubmit="return !1">
    <h5>Swap</h5>
    <label for="input-amount">Amount to send</label>
    <input id="input-amount" />

    <label for="output-amount">Amount to receive</label>
    <input id="output-amount" />

    <button>CONFIRM SWAP</button>
  </form>
  <script src="script.js"></script>
</body>

</html>

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class Database {
    constructor() {
        this.db = null;
        this.dbPath = path.join(__dirname, '../data/resources.db');
        this.init();
    }

    init() {
        // Ensure data directory exists
        const dataDir = path.dirname(this.dbPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
            console.log('📁 Created data directory');
        }

        // Connect to database
        this.db = new sqlite3.Database(this.dbPath, (err) => {
            if (err) {
                console.error('❌ Database connection error:', err.message);
            } else {
                console.log('🗄️ Connected to SQLite database');
                this.createTables();
            }
        });
    }

    createTables() {
        const createResourcesTable = `
            CREATE TABLE IF NOT EXISTS resources (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT NOT NULL,
                category TEXT NOT NULL DEFAULT 'General',
                status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'inactive')),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `;

        this.db.run(createResourcesTable, (err) => {
            if (err) {
                console.error('❌ Error creating resources table:', err.message);
            } else {
                console.log('✅ Resources table ready');
            }
        });
    }

    // Get database instance
    getDb() {
        return this.db;
    }

    // Run a query
    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    // Get single row
    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    // Get all rows
    all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // Close database connection
    close() {
        return new Promise((resolve, reject) => {
            this.db.close((err) => {
                if (err) {
                    reject(err);
                } else {
                    console.log('🔒 Database connection closed');
                    resolve();
                }
            });
        });
    }

    // Clear all data (useful for testing)
    async clearAllData() {
        try {
            await this.run('DELETE FROM resources');
            await this.run('DELETE FROM sqlite_sequence WHERE name="resources"');
            console.log('🧹 All data cleared');
            return { success: true, message: 'All data cleared' };
        } catch (error) {
            console.error('❌ Error clearing data:', error.message);
            return { success: false, error: error.message };
        }
    }
}

// Create singleton instance
const database = new Database();

module.exports = database;
